version: '3.8'

# Override file for scaling analysis workers
# Usage: docker-compose -f docker-compose.yml -f docker-compose.scale.yml up -d --scale analysis-worker=3

services:
  # Analysis Worker - Scaled Configuration
  analysis-worker:
    # Remove any fixed container naming to allow scaling
    # Docker will automatically name containers as:
    # atma-backend_analysis-worker_1, atma-backend_analysis-worker_2, etc.
    
    # Optional: Add hostname template for better identification
    hostname: analysis-worker-{{.Task.Slot}}
    
    # Environment variables that might need adjustment for scaling
    environment:
      # Worker ID for identification (will be overridden by Dock<PERSON>)
      WORKER_ID: "{{.Task.Slot}}"
      
      # Reduce concurrency per worker when scaling horizontally
      WORKER_CONCURRENCY: 3
      
      # Adjust heartbeat to avoid conflicts
      HEARTBEAT_INTERVAL: 300000
      
      # Load balancing: each worker processes different message types
      # (Optional: you can implement worker specialization)
      WORKER_TYPE: "general"
