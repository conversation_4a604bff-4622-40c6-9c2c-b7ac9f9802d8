@echo off
REM Script untuk mengelola analysis workers di Docker Compose

echo ========================================
echo   ATMA Analysis Workers Management
echo ========================================
echo.

:menu
echo Pilih opsi:
echo 1. Start semua workers (3 instances)
echo 2. Stop semua workers
echo 3. Restart semua workers
echo 4. Scale workers (custom number)
echo 5. Check status workers
echo 6. View logs worker 1
echo 7. View logs worker 2
echo 8. View logs worker 3
echo 9. Exit
echo.

set /p choice="Masukkan pilihan (1-9): "

if "%choice%"=="1" goto start_all
if "%choice%"=="2" goto stop_all
if "%choice%"=="3" goto restart_all
if "%choice%"=="4" goto scale_workers
if "%choice%"=="5" goto check_status
if "%choice%"=="6" goto logs_worker1
if "%choice%"=="7" goto logs_worker2
if "%choice%"=="8" goto logs_worker3
if "%choice%"=="9" goto exit

echo Pilihan tidak valid!
goto menu

:start_all
echo Starting all analysis workers...
docker-compose up -d analysis-worker-1 analysis-worker-2 analysis-worker-3
echo Workers started!
pause
goto menu

:stop_all
echo Stopping all analysis workers...
docker-compose stop analysis-worker-1 analysis-worker-2 analysis-worker-3
echo Workers stopped!
pause
goto menu

:restart_all
echo Restarting all analysis workers...
docker-compose restart analysis-worker-1 analysis-worker-2 analysis-worker-3
echo Workers restarted!
pause
goto menu

:scale_workers
set /p num="Masukkan jumlah workers yang diinginkan (1-10): "
echo Scaling to %num% workers...
REM Note: Ini menggunakan original service name untuk scaling
docker-compose up -d --scale analysis-worker=%num%
echo Scaled to %num% workers!
pause
goto menu

:check_status
echo Checking workers status...
docker-compose ps analysis-worker-1 analysis-worker-2 analysis-worker-3
pause
goto menu

:logs_worker1
echo Showing logs for analysis-worker-1...
docker-compose logs -f analysis-worker-1
goto menu

:logs_worker2
echo Showing logs for analysis-worker-2...
docker-compose logs -f analysis-worker-2
goto menu

:logs_worker3
echo Showing logs for analysis-worker-3...
docker-compose logs -f analysis-worker-3
goto menu

:exit
echo Goodbye!
exit /b 0
