#!/bin/bash

# Script untuk mengelola analysis workers di Docker Compose

echo "========================================"
echo "   ATMA Analysis Workers Management"
echo "========================================"
echo

show_menu() {
    echo "Pilih opsi:"
    echo "1. Start semua workers (3 instances)"
    echo "2. Stop semua workers"
    echo "3. Restart semua workers"
    echo "4. Scale workers (custom number)"
    echo "5. Check status workers"
    echo "6. View logs worker 1"
    echo "7. View logs worker 2"
    echo "8. View logs worker 3"
    echo "9. Exit"
    echo
}

start_all() {
    echo "Starting all analysis workers..."
    docker-compose up -d analysis-worker-1 analysis-worker-2 analysis-worker-3
    echo "Workers started!"
    read -p "Press Enter to continue..."
}

stop_all() {
    echo "Stopping all analysis workers..."
    docker-compose stop analysis-worker-1 analysis-worker-2 analysis-worker-3
    echo "Workers stopped!"
    read -p "Press Enter to continue..."
}

restart_all() {
    echo "Restarting all analysis workers..."
    docker-compose restart analysis-worker-1 analysis-worker-2 analysis-worker-3
    echo "Workers restarted!"
    read -p "Press Enter to continue..."
}

scale_workers() {
    read -p "Masukkan jumlah workers yang diinginkan (1-10): " num
    echo "Scaling to $num workers..."
    # Note: Ini menggunakan original service name untuk scaling
    docker-compose up -d --scale analysis-worker=$num
    echo "Scaled to $num workers!"
    read -p "Press Enter to continue..."
}

check_status() {
    echo "Checking workers status..."
    docker-compose ps analysis-worker-1 analysis-worker-2 analysis-worker-3
    read -p "Press Enter to continue..."
}

logs_worker1() {
    echo "Showing logs for analysis-worker-1..."
    docker-compose logs -f analysis-worker-1
}

logs_worker2() {
    echo "Showing logs for analysis-worker-2..."
    docker-compose logs -f analysis-worker-2
}

logs_worker3() {
    echo "Showing logs for analysis-worker-3..."
    docker-compose logs -f analysis-worker-3
}

# Main loop
while true; do
    show_menu
    read -p "Masukkan pilihan (1-9): " choice
    
    case $choice in
        1) start_all ;;
        2) stop_all ;;
        3) restart_all ;;
        4) scale_workers ;;
        5) check_status ;;
        6) logs_worker1 ;;
        7) logs_worker2 ;;
        8) logs_worker3 ;;
        9) echo "Goodbye!"; exit 0 ;;
        *) echo "Pilihan tidak valid!" ;;
    esac
    echo
done
